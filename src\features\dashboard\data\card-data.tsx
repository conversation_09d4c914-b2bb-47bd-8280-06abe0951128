// // /data/dashboardData.ts
// import { FaUsers, FaVideo, FaMoneyBill } from 'react-icons/fa';
// import { MdCategory } from 'react-icons/md';

// export const cardData = [
//   { icon: <FaUsers />, title: "Users", count: 1234, bgColor: "bg-pink-400" },
//   { icon: <MdCategory />, title: "Categories", count: 25, bgColor: "bg-purple-500" },
//   { icon: <FaMoneyBill />, title: "Revenue", count: 9870, bgColor: "bg-green-400" },
//   { icon: <FaVideo />, title: "Videos", count: 200, bgColor: "bg-orange-400" },
// ];


export let cardData = [
  {
    title: "Users",
    count: 1234,
    bgImage: "/assets/one-user.png", // Public URL path
  },
  {
    title: "Categories",
    count: 25,
    bgImage: "/assets/Group.png",
  },
  {
    title: "Revenue",
    count: 9870,
    bgImage: "/assets/revenue.png",
  },
  {
    title: "New Signups",
    count: 200,
    bgImage: "/assets/user.png",
  },
  {
    title: "Active Streams",
    count: 45,
    bgImage: "/assets/active.png", // Represents live-streaming activity
  },
  {
    title: "Subscribed Channels",
    count: 150,
    bgImage: "/assets/subscibed.png", // Related to channel subscriptions
  },
  
  {
    title: "Videos",
    count: 75,
    bgImage: "/assets/vdeo.png", // Daily/weekly user signups
  },
 

];


