"use client";

import {
  ColumnDef,
  SortingState,
  flexRender,
  ColumnFiltersState,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  useReactTable,
  getPaginationRowModel,
  PaginationState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useEffect, useMemo, useState } from "react";
import { DataTablePagination } from "./pagination";
import { useNavigate } from "react-router-dom";
import { Loader } from "@/components/globalfiles/loader";
import { Button } from "@/components/ui/button";
import {
  Dialog,
} from "@/components/ui/dialog";
import EditBlogModal from "./edit-blog";
import DeleteBlogModal from "./delete-blog";
import { BlogType } from "../../type/blogType";
import { createColumns } from "./column";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  loading: boolean | undefined;
  onPaginationChange: React.Dispatch<
    React.SetStateAction<{
      pageSize: number;
      pageIndex: number;
    }>
  >;
  pagination: {
    pageSize: number;
    pageIndex: number;
  };
  pageCount: number | undefined;
  title: string;
  onRefresh?: () => void; // Callback to refresh data after CRUD operations
}

export function DataTable<TData, TValue>({
  columns,
  data,
  loading,
  onRefresh,
}: DataTableProps<TData, TValue>) {

  console.log("DataTable received data:", data);
  console.log("DataTable data length:", data?.length);
  const navigate = useNavigate();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState<BlogType | null>(null);
  const [pages, setPageIndex] = useState<string>("25");
  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: 1,
    pageSize: Number(25),
  });

  const pagination = useMemo(
    () => ({
      pageIndex,
      pageSize,
    }),
    [pageIndex, pageSize]
  );

  useEffect(() => {
    setPagination({
      pageIndex: Number(0),
      pageSize: Number(pages),
    });
  }, [pages]);

  // CRUD Handlers
  const handleDelete = (blog: BlogType) => {
    setSelectedBlog(blog);
    setIsDeleteOpen(true);
  };

  const handleSuccess = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  // Create dynamic columns with CRUD handlers
  const dynamicColumns = useMemo(() => {
    return createColumns({
      onDelete: handleDelete,
    });
  }, []);

  // Use provided columns or dynamic columns
  const tableColumns = columns.length > 0 ? columns : dynamicColumns;

  const table = useReactTable({
    data,
    columns: tableColumns as ColumnDef<TData, TValue>[],
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      columnFilters,
      pagination,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-2 bg-[#FFEDE3] px-2 mt-2 mb-6 rounded-md">
        <div className="flex flex-row justify-start gap-3">
          <div className="rounded-md w-12 border-[1px] border-[#68473B]">
            <select
              className="w-full rounded-md h-10"
              name="page"
              id="page"
              onChange={(e) => setPageIndex(e.target.value)}
            >
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="75">75</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
        <div className="flex flex-row justify-between gap-5">
          <Button
            onClick={() => navigate('/blog/add')}
            className="bg-[#ee6620] flex items-center gap-2 text-white p-2 rounded-md hover:bg-[#d85a1c]"
          >
            Add Blog
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      className="bg-[#FFEDE3] text-[#313131]"
                      key={header.id}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="bg-white">
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <Loader />
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  className=""
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <DataTablePagination table={table} />

      {/* Edit Modal */}
      {selectedBlog && (
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <EditBlogModal
            blog={selectedBlog}
            setIsOpen={setIsEditOpen}
            onSuccess={handleSuccess}
          />
        </Dialog>
      )}

      {/* Delete Modal */}
      {selectedBlog && (
        <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
          <DeleteBlogModal
            blog={selectedBlog}
            setIsOpen={setIsDeleteOpen}
            onSuccess={handleSuccess}
          />
        </Dialog>
      )}
    </div>
  );
}